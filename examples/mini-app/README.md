# BetSwirl UI - React Casino Game Components

This is a **game widget library** for BetSwirl protocol casino games built with React + TypeScript + Vite.

## Installation

```bash
npm install @betswirl/ui
```

## Setup

### 1. Configure Tailwind CSS

Add the BetSwirl UI plugin to your `tailwind.config.js`:

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    // 👇 ВАЖНО: Добавьте путь к компонентам библиотеки
    "./node_modules/@betswirl/ui/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [
    // 👇 ВАЖНО: Подключите плагин BetSwirl UI
    require("@betswirl/ui/plugin"),
  ],
}
```

### 2. Use Components

```typescript
import React from "react"
import { AppProviders, RouletteGame } from "@betswirl/ui"

function App() {
  return (
    <AppProviders>
      <div className="min-h-screen bg-background p-4">
        <h1 className="text-2xl font-bold mb-4">Casino Games</h1>
        <RouletteGame />
      </div>
    </AppProviders>
  )
}

export default App
```

**Важно:** Больше не нужно импортировать CSS файлы - все стили генерируются автоматически через плагин Tailwind CSS.

## Available Components

- `RouletteGame` - Roulette game component
- `CoinTossGame` - Coin toss game component
- `DiceGame` - Dice game component
- `KenoGame` - Keno game component
- `AppProviders` - Required wrapper with providers

## Architecture

Эта библиотека использует архитектуру **плагина Tailwind CSS** для изоляции стилей:

- ✅ **Нет дублирования CSS** - утилиты Tailwind генерируются только один раз
- ✅ **Предсказуемые стили** - компоненты всегда имеют правильный приоритет
- ✅ **Эффективная сборка** - PurgeCSS работает корректно
- ✅ **Изоляция стилей** - базовые стили встраиваются в слой `@layer base`

Этот подход решает классические проблемы UI-библиотек с конфликтами стилей и обеспечивает надежную работу в любом проекте.

## Development Setup

The project uses:
- **React 19** + **TypeScript** for component development
- **Vite** for fast development and building
- **Biome** for linting and code formatting (replaces ESLint)
- **Storybook** for component development and testing
- **Tailwind CSS 4** for styling

## Code Quality

This project uses **Biome** for linting and formatting:

```bash
# Check code quality
pnpm lint

# Auto-fix issues
pnpm lint:fix

# Format code
pnpm format
```

Configuration is in `biome.json` and extends the root workspace configuration.

## Deploy

```shell
setfacl -R -m u:dev-components:rwx /var/www/betswirl-sdk/
```

1. Navigate to the mini-app folder:
```shell
cd examples/mini-app
```

2. Install dependencies (ignore workspace):
```shell
pnpm install --ignore-workspace
```

Start Storybook:
```shell
pnpm storybook
```

Start development server:
```bash
pnpm dev
```

Open in browser:
```
Storybook: http://localhost:6006/
Dev server: http://localhost:5173/
```

### Building and Publishing
- `pnpm build` - Build library for production
- `pnpm prepublishOnly` - Automatically runs build before publishing
