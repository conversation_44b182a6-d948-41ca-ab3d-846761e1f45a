# Testing the @betswirl/game-components Library

This guide explains how to test the library package in a separate React project.

## Quick Test Setup

### 1. Create a new React project

```bash
npx create-react-app test-casino-app --template typescript
cd test-casino-app
```

### 2. Install required dependencies

```bash
npm install @coinbase/onchainkit @tanstack/react-query viem wagmi tailwindcss
```

### 3. Install the library package

```bash
# Install from the packed tarball
npm install ../path/to/betswirl-game-components-0.1.0.tgz
```

### 4. Set up Tailwind CSS

```bash
npx tailwindcss init -p
```

Update `tailwind.config.js`:

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/@betswirl/game-components/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {},
  },
  plugins: [],
}
```

Add to `src/index.css`:

```css
@tailwind base;
@tailwind components;
@tailwind utilities;
```

### 5. Set up environment variables

Create `.env.local`:

```bash
REACT_APP_RPC_URL=your_rpc_url_here
REACT_APP_AFFILIATE_ADDRESS=your_affiliate_address_here
```

### 6. Update App.tsx

Replace the contents of `src/App.tsx`:

```typescript
import React from "react"
import "@betswirl/game-components/dist/game-components.css"
// Alternative: import '@betswirl/game-components/styles';
import { AppProviders, RouletteGame } from "@betswirl/game-components"

function App() {
  return (
    <AppProviders>
      <div className="min-h-screen bg-gray-100 p-4">
        <h1 className="text-2xl font-bold mb-4">Casino Games Test</h1>
        <RouletteGame />
      </div>
    </AppProviders>
  )
}

export default App
```

### 7. Run the test app

```bash
npm start
```

## Testing Different Components

### Test Coin Toss Game

```typescript
import { CoinTossGame } from "@betswirl/game-components"

// Replace RouletteGame with CoinTossGame in App.tsx
;<CoinTossGame />
```

### Test Dice Game

```typescript
import { DiceGame } from "@betswirl/game-components"

// Replace RouletteGame with DiceGame in App.tsx
;<DiceGame />
```

### Test Custom Themes

```typescript
<RouletteGame
  theme="dark"
  customTheme={{
    "--primary": "#ff6b35",
    "--play-btn-font": "bold",
  }}
/>
```

## Common Issues and Solutions

### 1. CSS not loading

- Make sure you import the CSS file: `import '@betswirl/game-components/dist/game-components.css'`
- Check that Tailwind CSS is properly configured

### 2. Components not rendering

- Ensure all peer dependencies are installed
- Check that AppProviders wrapper is used
- Verify environment variables are set

### 3. TypeScript errors

- Make sure TypeScript declarations are being found
- Check that the library was built with TypeScript declarations

### 4. Build errors

- Ensure all peer dependencies match the required versions
- Check for conflicting dependencies

## Expected Behavior

When everything is set up correctly, you should see:

1. A fully functional game interface
2. Wallet connection button
3. Game controls (betting interface)
4. Info and history panels
5. Proper styling and theming

The games should be interactive and ready for Web3 integration once proper RPC and affiliate settings are configured.
